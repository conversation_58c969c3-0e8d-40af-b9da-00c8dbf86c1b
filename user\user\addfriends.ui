<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>AddFriends</class>
 <widget class="QDialog" name="AddFriends">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>530</width>
    <height>530</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="contextMenuPolicy">
   <enum>Qt::ContextMenuPolicy::DefaultContextMenu</enum>
  </property>
  <property name="windowTitle">
   <string>添加好友</string>
  </property>
  <property name="toolTip">
   <string/>
  </property>
  <property name="styleSheet">
   <string notr="true">border:none</string>
  </property>
  <property name="modal">
   <bool>false</bool>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="1" alignment="Qt::AlignmentFlag::AlignRight">
    <widget class="QPushButton" name="but_deletewindow">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
       <horstretch>0</horstretch>
       <verstretch>15</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>20</width>
       <height>20</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>20</width>
       <height>20</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    border: none;
    background: transparent;
    color: rgb(78, 78, 78);
    font:  16pt 'Microsoft New Tai Lue';
}

QPushButton:hover {
    border: none;
    background: transparent;
    color: white;
    font:  16pt 'Microsoft New Tai Lue';
    background: red;
    border-radius: 3px;
}</string>
     </property>
     <property name="text">
      <string>x</string>
     </property>
     <property name="autoDefault">
      <bool>false</bool>
     </property>
    </widget>
   </item>
   <item row="1" column="0" colspan="3">
    <layout class="QHBoxLayout" name="horizontalLayout_3">
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout">
       <item>
        <widget class="LineSerach_2" name="line_serach">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>480</width>
           <height>30</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>460</width>
           <height>30</height>
          </size>
         </property>
         <property name="mouseTracking">
          <bool>false</bool>
         </property>
         <property name="focusPolicy">
          <enum>Qt::FocusPolicy::ClickFocus</enum>
         </property>
         <property name="styleSheet">
          <string notr="true">border-radius: 5px;
border: none;
padding:5px;
font: 400 11pt &quot;Microsoft YaHei UI Light&quot;;
color:grey;</string>
         </property>
         <property name="text">
          <string>输入对方账号搜索</string>
         </property>
         <property name="maxLength">
          <number>12</number>
         </property>
        </widget>
       </item>
       <item alignment="Qt::AlignmentFlag::AlignLeft">
        <widget class="QPushButton" name="but_fangdajing">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>30</width>
           <height>30</height>
          </size>
         </property>
         <property name="cursor">
          <cursorShape>PointingHandCursor</cursorShape>
         </property>
         <property name="styleSheet">
          <string notr="true">background:transparent;
 border: none;
</string>
         </property>
         <property name="text">
          <string/>
         </property>
         <property name="icon">
          <iconset theme="QIcon::ThemeIcon::EditFind"/>
         </property>
         <property name="autoDefault">
          <bool>false</bool>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item row="2" column="0" colspan="3">
    <widget class="QFrame" name="frame_message">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
       <horstretch>0</horstretch>
       <verstretch>210</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>210</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">border-radius: 10px;
border: 0.5px solid rgb(180, 180, 180);
</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <item row="0" column="0">
       <layout class="QVBoxLayout" name="layout_frame"/>
      </item>
     </layout>
    </widget>
   </item>
   <item row="0" column="0">
    <widget class="QLabel" name="label">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="contextMenuPolicy">
      <enum>Qt::ContextMenuPolicy::NoContextMenu</enum>
     </property>
     <property name="styleSheet">
      <string notr="true">font: 700 11pt &quot;Microsoft YaHei UI&quot;;</string>
     </property>
     <property name="text">
      <string>     添加好友</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignmentFlag::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item row="0" column="2">
    <spacer name="horizontalSpacer_4">
     <property name="orientation">
      <enum>Qt::Orientation::Horizontal</enum>
     </property>
     <property name="sizeType">
      <enum>QSizePolicy::Policy::Fixed</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>1</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>LineSerach_2</class>
   <extends>QLineEdit</extends>
   <header location="global">addfriends.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
