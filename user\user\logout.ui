<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Logout</class>
 <widget class="QDialog" name="Logout">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>302</width>
    <height>187</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <property name="styleSheet">
   <string notr="true">border:none;</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_4">
   <item row="0" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item alignment="Qt::AlignmentFlag::AlignTop">
      <widget class="QLabel" name="label">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="styleSheet">
        <string notr="true">font: 700 11pt &quot;Microsoft YaHei UI&quot;;
border:none;</string>
       </property>
       <property name="text">
        <string>         注销账号</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignmentFlag::AlignCenter</set>
       </property>
      </widget>
     </item>
     <item alignment="Qt::AlignmentFlag::AlignTop">
      <widget class="QPushButton" name="but_deletewindow">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>15</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>20</width>
         <height>20</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>20</width>
         <height>20</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
    border: none;
    background: transparent;
    color: rgb(78, 78, 78);
    font:  16pt 'Microsoft New Tai Lue';
}

QPushButton:hover {
    border: none;
    background: transparent;
    color: white;
    font:  16pt 'Microsoft New Tai Lue';
    background: red;
    border-radius: 3px;
}</string>
       </property>
       <property name="text">
        <string>x</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="1" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_10">
     <item>
      <widget class="QLabel" name="lab_account">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Maximum" vsizetype="Maximum">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>50</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">font: 12pt &quot;Microsoft YaHei UI&quot;;
border:none;
color:black;
</string>
       </property>
       <property name="text">
        <string>输入账号:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="LineChangePass" name="line_account">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Maximum">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>224</width>
         <height>38</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>224</width>
         <height>38</height>
        </size>
       </property>
       <property name="focusPolicy">
        <enum>Qt::FocusPolicy::ClickFocus</enum>
       </property>
       <property name="contextMenuPolicy">
        <enum>Qt::ContextMenuPolicy::NoContextMenu</enum>
       </property>
       <property name="styleSheet">
        <string notr="true">font: 12pt &quot;Microsoft YaHei UI&quot;;
border: 1px solid rgba(0, 0, 0, 0.1);
border-radius: 8px;
padding:5px;
color:grey;

</string>
       </property>
       <property name="text">
        <string>请输入您的账号</string>
       </property>
       <property name="maxLength">
        <number>12</number>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="2" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_9">
     <item>
      <widget class="QLabel" name="lab_password">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Maximum" vsizetype="Maximum">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>50</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">font: 12pt &quot;Microsoft YaHei UI&quot;;
border:none;
color:black;
</string>
       </property>
       <property name="text">
        <string>输入密码:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="LineChangePass" name="line_password">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Maximum">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>224</width>
         <height>38</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>224</width>
         <height>38</height>
        </size>
       </property>
       <property name="focusPolicy">
        <enum>Qt::FocusPolicy::ClickFocus</enum>
       </property>
       <property name="contextMenuPolicy">
        <enum>Qt::ContextMenuPolicy::NoContextMenu</enum>
       </property>
       <property name="styleSheet">
        <string notr="true">font: 12pt &quot;Microsoft YaHei UI&quot;;
border: 1px solid rgba(0, 0, 0, 0.1);
border-radius: 8px;
padding:5px;
color:grey;

</string>
       </property>
       <property name="text">
        <string>请输入您的密码</string>
       </property>
       <property name="maxLength">
        <number>12</number>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="3" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_8">
     <item>
      <spacer name="horizontalSpacer_5">
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="but_yes">
       <property name="enabled">
        <bool>false</bool>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>175</width>
         <height>35</height>
        </size>
       </property>
       <property name="cursor">
        <cursorShape>PointingHandCursor</cursorShape>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
    font: 12pt &quot;Microsoft YaHei UI&quot;;
    background-color: rgb(167, 214, 255); 
    color: white;
    border-radius: 10px;
}
</string>
       </property>
       <property name="text">
        <string>确认</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_6">
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>LineChangePass</class>
   <extends>QLineEdit</extends>
   <header location="global">changepassword.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
