<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FindPassword</class>
 <widget class="QMainWindow" name="FindPassword">
  <property name="windowModality">
   <enum>Qt::WindowModality::WindowModal</enum>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>390</width>
    <height>203</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>390</width>
    <height>203</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>390</width>
    <height>203</height>
   </size>
  </property>
  <property name="contextMenuPolicy">
   <enum>Qt::ContextMenuPolicy::NoContextMenu</enum>
  </property>
  <property name="windowTitle">
   <string>找回密码</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout_2">
    <item row="0" column="0">
     <spacer name="verticalSpacer">
      <property name="orientation">
       <enum>Qt::Orientation::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>371</width>
        <height>11</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="1" column="0" colspan="2">
     <widget class="QStackedWidget" name="page_find">
      <property name="focusPolicy">
       <enum>Qt::FocusPolicy::NoFocus</enum>
      </property>
      <property name="currentIndex">
       <number>0</number>
      </property>
      <widget class="QWidget" name="pages_1">
       <layout class="QVBoxLayout" name="verticalLayout">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout">
          <item>
           <spacer name="horizontalSpacer_5">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="lab_pages1">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Maximum" vsizetype="Maximum">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="styleSheet">
             <string notr="true">font: 13pt &quot;Microsoft YaHei UI&quot;;

color:black;
</string>
            </property>
            <property name="text">
             <string>请输入要找回的账号</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_4">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_2">
          <item>
           <spacer name="horizontalSpacer_2">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="LineFind" name="line_pages1">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>224</width>
              <height>38</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>224</width>
              <height>38</height>
             </size>
            </property>
            <property name="focusPolicy">
             <enum>Qt::FocusPolicy::ClickFocus</enum>
            </property>
            <property name="contextMenuPolicy">
             <enum>Qt::ContextMenuPolicy::NoContextMenu</enum>
            </property>
            <property name="styleSheet">
             <string notr="true">font: 13pt &quot;Microsoft YaHei UI&quot;;
border: 1px solid rgba(0, 0, 0, 0.3);
border-radius: 8px;
color:grey;
</string>
            </property>
            <property name="text">
             <string>请输入你的账号</string>
            </property>
            <property name="maxLength">
             <number>12</number>
            </property>
            <property name="alignment">
             <set>Qt::AlignmentFlag::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_3">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_3">
          <item>
           <spacer name="horizontalSpacer">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>286</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="but_pages1">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
              <horstretch>20</horstretch>
              <verstretch>20</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>50</width>
              <height>35</height>
             </size>
            </property>
            <property name="cursor">
             <cursorShape>PointingHandCursor</cursorShape>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton {
    font: 10pt &quot;Microsoft YaHei UI&quot;;
    background-color: rgb(50, 150, 255);
    border: 1px solid rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    color: white;
}
QPushButton:hover {
    background-color: rgba(50, 150, 255, 0.7);
    border: 1px solid rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    color: white;
}
QPushButton:pressed {
    background-color: rgba(0, 100, 255, 0.8);
    border: 1px solid rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    color: white;
}</string>
            </property>
            <property name="text">
             <string>下一步</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="pages_2">
       <layout class="QVBoxLayout" name="verticalLayout_5">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_15">
          <item>
           <spacer name="horizontalSpacer_21">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="lab_pages2">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Maximum" vsizetype="Maximum">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="styleSheet">
             <string notr="true">font: 13pt &quot;Microsoft YaHei UI&quot;;

color:black;
</string>
            </property>
            <property name="text">
             <string>假装这是一个密保问题</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_22">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_14">
          <item>
           <spacer name="horizontalSpacer_23">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="LineFind" name="line_pages2">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>224</width>
              <height>38</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>224</width>
              <height>38</height>
             </size>
            </property>
            <property name="focusPolicy">
             <enum>Qt::FocusPolicy::ClickFocus</enum>
            </property>
            <property name="contextMenuPolicy">
             <enum>Qt::ContextMenuPolicy::NoContextMenu</enum>
            </property>
            <property name="styleSheet">
             <string notr="true">font: 13pt &quot;Microsoft YaHei UI&quot;;
border: 1px solid rgba(0, 0, 0, 0.3);
border-radius: 8px;
color:grey;
</string>
            </property>
            <property name="text">
             <string>请输入密保答案</string>
            </property>
            <property name="maxLength">
             <number>12</number>
            </property>
            <property name="alignment">
             <set>Qt::AlignmentFlag::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_24">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_13">
          <item>
           <spacer name="horizontalSpacer_25">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="but_pages2">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
              <horstretch>20</horstretch>
              <verstretch>20</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>50</width>
              <height>35</height>
             </size>
            </property>
            <property name="cursor">
             <cursorShape>PointingHandCursor</cursorShape>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton {
    font: 10pt &quot;Microsoft YaHei UI&quot;;
    background-color: rgb(50, 150, 255);
    border: 1px solid rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    color: white;
}
QPushButton:hover {
    background-color: rgba(50, 150, 255, 0.7);
    border: 1px solid rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    color: white;
}
QPushButton:pressed {
    background-color: rgba(0, 100, 255, 0.8);
    border: 1px solid rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    color: white;
}</string>
            </property>
            <property name="text">
             <string>下一步</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="pages_3">
       <layout class="QGridLayout" name="gridLayout">
        <item row="0" column="0">
         <layout class="QHBoxLayout" name="horizontalLayout_16">
          <item>
           <spacer name="horizontalSpacer_26">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="LineFind" name="line_pages3_2">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>224</width>
              <height>38</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>224</width>
              <height>38</height>
             </size>
            </property>
            <property name="focusPolicy">
             <enum>Qt::FocusPolicy::ClickFocus</enum>
            </property>
            <property name="contextMenuPolicy">
             <enum>Qt::ContextMenuPolicy::NoContextMenu</enum>
            </property>
            <property name="styleSheet">
             <string notr="true">font: 13pt &quot;Microsoft YaHei UI&quot;;
border: 1px solid rgba(0, 0, 0, 0.3);
border-radius: 8px;
color:grey;
</string>
            </property>
            <property name="text">
             <string>请输入新的密码</string>
            </property>
            <property name="maxLength">
             <number>12</number>
            </property>
            <property name="alignment">
             <set>Qt::AlignmentFlag::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_27">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item row="1" column="0">
         <layout class="QHBoxLayout" name="horizontalLayout_17">
          <item>
           <spacer name="horizontalSpacer_28">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="LineFind" name="line_pages3">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>224</width>
              <height>38</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>224</width>
              <height>38</height>
             </size>
            </property>
            <property name="focusPolicy">
             <enum>Qt::FocusPolicy::ClickFocus</enum>
            </property>
            <property name="contextMenuPolicy">
             <enum>Qt::ContextMenuPolicy::NoContextMenu</enum>
            </property>
            <property name="styleSheet">
             <string notr="true">font: 13pt &quot;Microsoft YaHei UI&quot;;
border: 1px solid rgba(0, 0, 0, 0.3);
border-radius: 8px;
color:grey;
</string>
            </property>
            <property name="text">
             <string>请确认密码</string>
            </property>
            <property name="maxLength">
             <number>12</number>
            </property>
            <property name="alignment">
             <set>Qt::AlignmentFlag::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_29">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item row="2" column="0">
         <layout class="QHBoxLayout" name="horizontalLayout_18">
          <item>
           <spacer name="horizontalSpacer_30">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="but_pages3">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
              <horstretch>20</horstretch>
              <verstretch>20</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>50</width>
              <height>35</height>
             </size>
            </property>
            <property name="cursor">
             <cursorShape>PointingHandCursor</cursorShape>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton {
    font: 10pt &quot;Microsoft YaHei UI&quot;;
    background-color: rgb(50, 150, 255);
    border: 1px solid rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    color: white;
}
QPushButton:hover {
    background-color: rgba(50, 150, 255, 0.7);
    border: 1px solid rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    color: white;
}
QPushButton:pressed {
    background-color: rgba(0, 100, 255, 0.8);
    border: 1px solid rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    color: white;
}</string>
            </property>
            <property name="text">
             <string>完成</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>390</width>
     <height>18</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <customwidgets>
  <customwidget>
   <class>LineFind</class>
   <extends>QLineEdit</extends>
   <header location="global">findpassword.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
